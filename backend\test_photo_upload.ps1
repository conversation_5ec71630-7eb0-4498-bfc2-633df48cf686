# Test script to verify photo upload functionality with RLS policies
# This script tests the storage policies we just applied

Write-Host "🧪 Testing Photo Upload with RLS Policies..." -ForegroundColor Green
Write-Host ""

# Configuration
$SUPABASE_URL = "http://localhost:8000"
$ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"

# Test 1: Check if media bucket exists
Write-Host "🪣 Test 1: Check Media Bucket" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$SUPABASE_URL/storage/v1/bucket/media" -Headers @{
        "Authorization" = "Bearer $ANON_KEY"
        "apikey" = $ANON_KEY
    } -Method GET
    Write-Host "✅ Media bucket exists: $($response.name)" -ForegroundColor Green
} catch {
    Write-Host "❌ Media bucket check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Authenticate user
Write-Host ""
Write-Host "🔐 Test 2: User Authentication" -ForegroundColor Yellow
try {
    $authBody = @{
        email = "<EMAIL>"
        password = "password123"
    } | ConvertTo-Json

    $authResponse = Invoke-RestMethod -Uri "$SUPABASE_URL/auth/v1/token?grant_type=password" -Headers @{
        "Content-Type" = "application/json"
        "apikey" = $ANON_KEY
    } -Method POST -Body $authBody

    $accessToken = $authResponse.access_token
    $userId = $authResponse.user.id
    Write-Host "✅ Authentication successful" -ForegroundColor Green
    Write-Host "User ID: $userId" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Create a test image file
Write-Host ""
Write-Host "📸 Test 3: Create Test Image" -ForegroundColor Yellow
$testImageBytes = [byte[]](0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82)
$tempImagePath = [System.IO.Path]::GetTempFileName() + ".png"
[System.IO.File]::WriteAllBytes($tempImagePath, $testImageBytes)
Write-Host "✅ Test image created: $tempImagePath" -ForegroundColor Green

# Test 4: Upload image to storage
Write-Host ""
Write-Host "📤 Test 4: Upload Image to Storage" -ForegroundColor Yellow
try {
    $timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
    $fileName = "uploads/${userId}_${timestamp}.png"
    
    $uploadResponse = Invoke-RestMethod -Uri "$SUPABASE_URL/storage/v1/object/media/$fileName" -Headers @{
        "Authorization" = "Bearer $accessToken"
        "apikey" = $ANON_KEY
        "Content-Type" = "image/png"
    } -Method POST -InFile $tempImagePath

    Write-Host "✅ Image uploaded successfully" -ForegroundColor Green
    Write-Host "File path: $fileName" -ForegroundColor Cyan
    
    # Get public URL
    $publicUrl = "$SUPABASE_URL/storage/v1/object/public/media/$fileName"
    Write-Host "Public URL: $publicUrl" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Image upload failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# Test 5: Create post with uploaded image
Write-Host ""
Write-Host "📝 Test 5: Create Post with Image" -ForegroundColor Yellow
try {
    $postBody = @{
        user_id = $userId
        media_url = $publicUrl
        media_type = "image"
        content = "Test post with real photo upload from PowerShell"
        channel_id = $null
    } | ConvertTo-Json

    $postResponse = Invoke-RestMethod -Uri "$SUPABASE_URL/rest/v1/posts" -Headers @{
        "Authorization" = "Bearer $accessToken"
        "apikey" = $ANON_KEY
        "Content-Type" = "application/json"
        "Prefer" = "return=representation"
    } -Method POST -Body $postBody

    $postId = $postResponse.id
    Write-Host "✅ Post created successfully!" -ForegroundColor Green
    Write-Host "Post ID: $postId" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Post creation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# Test 6: Clean up
Write-Host ""
Write-Host "🧹 Test 6: Clean Up" -ForegroundColor Yellow
try {
    # Delete post
    Invoke-RestMethod -Uri "$SUPABASE_URL/rest/v1/posts?id=eq.$postId" -Headers @{
        "Authorization" = "Bearer $accessToken"
        "apikey" = $ANON_KEY
    } -Method DELETE
    
    # Delete image
    Invoke-RestMethod -Uri "$SUPABASE_URL/storage/v1/object/media/$fileName" -Headers @{
        "Authorization" = "Bearer $accessToken"
        "apikey" = $ANON_KEY
    } -Method DELETE
    
    # Delete temp file
    Remove-Item $tempImagePath -Force
    
    Write-Host "✅ Clean up completed" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️  Clean up had issues: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 All tests passed! Photo upload with RLS is working correctly." -ForegroundColor Green
Write-Host "✅ Storage policies are properly configured" -ForegroundColor Green
Write-Host "✅ Authenticated users can upload photos" -ForegroundColor Green
Write-Host "✅ Posts can be created with uploaded images" -ForegroundColor Green
