-- Apply storage policies for media bucket
-- Create the media bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'media',
    'media',
    true,
    52428800,
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/webm']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Media files are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload media files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own media files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own media files" ON storage.objects;

-- Policy: Allow public read access to media files
CREATE POLICY "Media files are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'media');

-- Policy: Allow authenticated users to upload media files
CREATE POLICY "Authenticated users can upload media files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media' 
        AND auth.role() = 'authenticated'
    );

-- Policy: Allow users to update their own media files
CREATE POLICY "Users can update their own media files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    ) WITH CHECK (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy: Allow users to delete their own media files
CREATE POLICY "Users can delete their own media files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

SELECT 'Storage policies applied successfully' AS status;
