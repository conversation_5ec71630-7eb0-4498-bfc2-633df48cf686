import 'dart:io';
import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Simple test script to verify photo upload functionality
/// This bypasses Flutter test framework issues and directly tests the RLS policies
void main() async {
  print('🧪 Testing Photo Upload with RLS Policies...\n');

  try {
    // Initialize Supabase
    print('🔧 Initializing Supabase...');
    await Supabase.initialize(
      url: 'http://localhost:8000',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
    );
    final client = Supabase.instance.client;
    print('✅ Supabase initialized successfully\n');

    // Test 1: Check database connection
    print('📊 Test 1: Database Connection');
    try {
      final response = await client.from('posts').select('count').count();
      print('✅ Database connection successful. Post count: $response\n');
    } catch (e) {
      print('❌ Database connection failed: $e\n');
      return;
    }

    // Test 2: Check storage bucket access
    print('🪣 Test 2: Storage Bucket Access');
    try {
      final buckets = await client.storage.listBuckets();
      print('✅ Storage accessible. Buckets: ${buckets.map((b) => b.name).join(', ')}');
      
      final mediaBucket = buckets.where((b) => b.name == 'media').firstOrNull;
      if (mediaBucket != null) {
        print('✅ Media bucket found: ${mediaBucket.name}\n');
      } else {
        print('❌ Media bucket not found\n');
        return;
      }
    } catch (e) {
      print('❌ Storage access failed: $e\n');
      return;
    }

    // Test 3: Authentication
    print('🔐 Test 3: Authentication');
    try {
      final authResponse = await client.auth.signInWithPassword(
        email: '<EMAIL>',
        password: 'password123',
      );

      if (authResponse.user != null) {
        print('✅ Authentication successful');
        print('User ID: ${authResponse.user!.id}');
        print('User Email: ${authResponse.user!.email}\n');
      } else {
        print('❌ Authentication failed - no user returned\n');
        return;
      }
    } catch (e) {
      print('❌ Authentication failed: $e\n');
      return;
    }

    // Test 4: Photo Upload
    print('📸 Test 4: Photo Upload');
    try {
      // Create a test image file (1x1 pixel PNG)
      final testImageBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,
        0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, 0x00, 0x00, // IEND chunk
        0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
      ]);

      // Generate unique filename
      final user = client.auth.currentUser!;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${user.id}_$timestamp.png';
      final filePath = 'uploads/$fileName';

      print('📤 Uploading test image: $filePath');

      // Upload to Supabase storage
      await client.storage.from('media').uploadBinary(filePath, testImageBytes);
      print('✅ Image uploaded successfully');

      // Get public URL
      final publicUrl = client.storage.from('media').getPublicUrl(filePath);
      print('🔗 Public URL: $publicUrl');

      // Test 5: Create post with uploaded image
      print('\n📝 Test 5: Create Post with Image');
      final postResponse = await client.from('posts').insert({
        'user_id': user.id,
        'media_url': publicUrl,
        'media_type': 'image',
        'content': 'Test post with real photo upload',
        'channel_id': null,
      }).select().single();

      print('✅ Post created successfully!');
      print('Post ID: ${postResponse['id']}');

      // Clean up - delete the test post and image
      print('\n🧹 Cleaning up...');
      await client.from('posts').delete().eq('id', postResponse['id']);
      await client.storage.from('media').remove([filePath]);
      print('✅ Test data cleaned up');

      print('\n🎉 All tests passed! Photo upload with RLS is working correctly.');

    } catch (e) {
      print('❌ Photo upload test failed: $e');
      
      // Provide specific guidance based on error type
      if (e.toString().contains('storage') || e.toString().contains('bucket')) {
        print('💡 This appears to be a storage bucket or RLS policy issue');
        print('💡 Check that storage policies exist for the media bucket');
      } else if (e.toString().contains('auth') || e.toString().contains('401')) {
        print('💡 This appears to be an authentication issue');
        print('💡 Check that the test user exists and credentials are correct');
      } else if (e.toString().contains('policy') || e.toString().contains('permission')) {
        print('💡 This appears to be an RLS policy issue');
        print('💡 Check that the storage policies allow authenticated users to upload');
      }
      
      print('\n❌ Photo upload test failed - RLS policies may need to be fixed');
    }

  } catch (e) {
    print('❌ Test setup failed: $e');
  }
}
