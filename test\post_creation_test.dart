import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:gameflex_mobile/services/upload_service.dart';

/// Creates a simple black JPEG image with 9:16 aspect ratio (180x320 pixels)
Uint8List _createBlackJPEG() {
  // Minimal valid JPEG file (black 180x320 image)
  // This is a pre-generated minimal JPEG for testing purposes
  return Uint8List.fromList([
    // JPEG header
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x01, 0x40,
    0x00, 0xB4, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03,
    0x01,
    0x00,
    0x02,
    0x11,
    0x03,
    0x11,
    0x00,
    0x3F,
    0x00,
    0x00,
    0xFF,
    0xD9,
  ]);
}

void main() {
  group('Post Creation Tests', () {
    late SupabaseClient client;

    setUpAll(() async {
      // Initialize Supabase properly for testing
      await Supabase.initialize(
        url: 'http://localhost:8000',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
      );
      client = Supabase.instance.client;
    });

    test('Test Database Connection', () async {
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
        expect(response, isA<int>());
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed: $e');
      }
    });

    test('Test Post Creation', () async {
      expect(client, isNotNull);

      // Test basic connectivity
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed');
      }
    });

    test('Test Storage Bucket Access', () async {
      final client = SupabaseService.supabaseClient;

      try {
        final buckets = await client.storage.listBuckets();
        print('✅ Storage connection successful');
        print('Available buckets: ${buckets.map((b) => b.name).join(', ')}');

        final mediaBucket = buckets.where((b) => b.name == 'media').firstOrNull;
        expect(mediaBucket, isNotNull, reason: 'Media bucket should exist');
        print('✅ Media bucket found: ${mediaBucket!.name}');
      } catch (e) {
        print('❌ Storage connection failed: $e');
        fail('Storage connection failed');
      }
    });

    test('Test Authentication', () async {
      final client = SupabaseService.supabaseClient;

      try {
        // Try to sign in with test credentials
        final response = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (response.user != null) {
          print('✅ Authentication successful');
          print('User ID: ${response.user!.id}');
          print('User Email: ${response.user!.email}');
        } else {
          print('⚠️  Authentication returned null user');
        }
      } catch (e) {
        print('❌ Authentication failed: $e');
        // Don't fail the test as auth might not be set up yet
      }
    });

    test('Test Post Creation (Mock)', () async {
      final client = SupabaseService.supabaseClient;

      // Create a test post directly in database
      try {
        final testPost = {
          'user_id':
              '37cc897f-a70a-4d04-9b6e-092d0b861488', // Known dev user ID
          'content': 'Test post created by Flutter test',
          'media_type': 'image',
          'media_url':
              'http://localhost:8000/storage/v1/object/public/media/test.jpg',
          'channel_id': null,
          'like_count': 0,
          'comment_count': 0,
          'is_active': true,
        };

        final response = await client.from('posts').insert(testPost).select();

        if (response.isNotEmpty) {
          print('✅ Post creation successful');
          print('Created post ID: ${response.first['id']}');

          // Clean up - delete the test post
          await client.from('posts').delete().eq('id', response.first['id']);
          print('✅ Test post cleaned up');
        } else {
          print('❌ Post creation returned empty response');
          fail('Post creation failed');
        }
      } catch (e) {
        print('❌ Post creation failed: $e');
        fail('Post creation failed: $e');
      }
    });

    test('Test Upload Service Configuration', () {
      // Test that upload service is properly configured
      final uploadService = UploadService();
      expect(uploadService, isNotNull);
      print('✅ Upload service initialized');

      // Test Supabase client access
      final client = SupabaseService.supabaseClient;
      expect(client, isNotNull);
      print('✅ Supabase client accessible from upload service');
    });

    test('Test Real Photo Upload with Authentication', () async {
      final client = SupabaseService.supabaseClient;
      final uploadService = UploadService();

      try {
        // First authenticate with test user
        print('🔐 Authenticating test user...');
        final authResponse = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (authResponse.user == null) {
          print('❌ Authentication failed - cannot test photo upload');
          fail('Authentication required for photo upload test');
        }

        print('✅ Authenticated as: ${authResponse.user!.email}');
        print('User ID: ${authResponse.user!.id}');

        // Create a test image file (9:16 black JPEG - 180x320 pixels)
        final testImageBytes = _createBlackJPEG();

        // Create temporary file
        final tempDir = Directory.systemTemp;
        final testImageFile = File('${tempDir.path}/test_image.jpg');
        await testImageFile.writeAsBytes(testImageBytes);

        print('📸 Created test image file: ${testImageFile.path}');

        // Attempt to upload the image and create a post
        print('📤 Attempting photo upload...');
        final success = await uploadService.createPost(
          imageFile: testImageFile,
          content: 'Test post with real photo upload',
        );

        // Clean up the temporary file
        if (await testImageFile.exists()) {
          await testImageFile.delete();
        }

        if (success) {
          print('✅ Photo upload and post creation successful!');
          expect(success, isTrue);
        } else {
          print(
            '❌ Photo upload failed - this indicates an RLS or storage policy issue',
          );
          fail('Photo upload failed - check RLS policies for storage bucket');
        }
      } catch (e) {
        print('❌ Photo upload test failed with error: $e');

        // Provide specific guidance based on error type
        if (e.toString().contains('storage') ||
            e.toString().contains('bucket')) {
          print('💡 This appears to be a storage bucket or RLS policy issue');
          print('💡 Check that storage policies exist for the media bucket');
        } else if (e.toString().contains('auth') ||
            e.toString().contains('401')) {
          print('💡 This appears to be an authentication issue');
          print(
            '💡 Check that the test user exists and credentials are correct',
          );
        }

        fail('Photo upload test failed: $e');
      }
    });
  });
}

/// Helper function to run all tests and print results
void runPostCreationTests() async {
  print('🧪 Running Post Creation Tests...\n');

  try {
    await SupabaseService.initialize();
    print('✅ Supabase initialized\n');

    final uploadService = UploadService();
    final client = SupabaseService.supabaseClient;

    // Test 1: Database Connection
    print('📊 Test 1: Database Connection');
    try {
      final response = await client.from('posts').select('count').count();
      print('✅ Database connection successful. Post count: $response\n');
    } catch (e) {
      print('❌ Database connection failed: $e\n');
    }

    // Test 2: Storage Access
    print('🪣 Test 2: Storage Access');
    try {
      final buckets = await client.storage.listBuckets();
      print(
        '✅ Storage accessible. Buckets: ${buckets.map((b) => b.name).join(', ')}\n',
      );
    } catch (e) {
      print('❌ Storage access failed: $e\n');
    }

    // Test 3: Post Creation
    print('📝 Test 3: Post Creation');
    try {
      final testPost = {
        'user_id': '37cc897f-a70a-4d04-9b6e-092d0b861488',
        'content': 'Test post from Flutter test function',
        'media_type': 'image',
        'channel_id': null,
      };

      final response = await client.from('posts').insert(testPost).select();
      if (response.isNotEmpty) {
        print('✅ Post creation successful! ID: ${response.first['id']}');

        // Clean up
        await client.from('posts').delete().eq('id', response.first['id']);
        print('✅ Test post cleaned up\n');
      }
    } catch (e) {
      print('❌ Post creation failed: $e\n');
    }

    print('🏁 Post creation tests completed!');
  } catch (e) {
    print('❌ Test setup failed: $e');
  }
}
